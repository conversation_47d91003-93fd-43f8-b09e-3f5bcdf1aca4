export const API_ENDPOINTS = {
  CRYPTO_STATISTICS: '/api/v1/crypto/statistics',
  CRYPTO_INDICATORS: '/api/v1/crypto/indicators',
  STOCK_STATISTICS: '/api/v1/stock/statistics',
  STOCK_INDICATORS: '/api/v1/stock/indicators',
} as const;

export const API_DOCS = {
  CRYPTO_STATISTICS: {
    SUMMARY: 'Get cryptocurrency statistics',
    DESCRIPTION: 'Retrieve current cryptocurrency statistics with technical indicators',
  },
  CRYPTO_INDICATORS: {
    SUMMARY: 'Get historical indicator data for a specific cryptocurrency',
    DESCRIPTION: 'Retrieve time-series technical indicator data for charting and analysis',
  },
  STOCK_STATISTICS: {
    SUMMARY: 'Get stock statistics',
    DESCRIPTION: 'Retrieve current stock statistics with technical indicators',
  },
  STOCK_INDICATORS: {
    SUMMARY: 'Get historical indicator data for a specific stock',
    DESCRIPTION: 'Retrieve time-series technical indicator data for stock charting and analysis',
  },
} as const;

export const QUERY_PARAMS = {
  SYMBOL: {
    NAME: 'symbol',
    DESCRIPTION: 'Cryptocurrency symbol (e.g., BTC, ETH, SOL)',
    EXAMPLE: 'ETH',
  },
  CONVERSION_CURRENCY: {
    NAME: 'conversionCurrency',
    DESCRIPTION: 'Conversion currency (USD or BTC)',
    EXAMPLE: 'USD',
  },
} as const;

export const API_ERROR_MESSAGES = {
  MISSING_REQUIRED_PARAMS: 'Symbol and conversionCurrency are required',
  FETCH_FAILED: 'Failed to fetch data from daemon',
  INVALID_SYMBOL: 'Invalid cryptocurrency symbol',
  INVALID_CURRENCY: 'Invalid conversion currency',
} as const;

export const API_TAGS = {
  CRYPTO_STATISTICS: 'Crypto Statistics',
  STOCK_STATISTICS: 'Stock Statistics',
} as const;
