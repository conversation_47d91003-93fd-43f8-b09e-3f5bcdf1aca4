import { ApiProperty } from '@nestjs/swagger';

export class StockMappingDto {
  @ApiProperty({ example: 'AAPL' })
  symbol!: string;

  @ApiProperty({ example: 'Apple Inc.' })
  name!: string;

  @ApiProperty({ example: 'NASDAQ' })
  exchange!: string;

  @ApiProperty({ example: 'USD' })
  currency!: string;

  @ApiProperty({ example: 'US' })
  country!: string;

  @ApiProperty({ example: 'equity' })
  type!: string;
}

export class StockIndicatorValueDto {
  @ApiProperty() open!: number;
  @ApiProperty() high!: number;
  @ApiProperty() low!: number;
  @ApiProperty() close!: number;
  @ApiProperty() volume!: number;
  @ApiProperty({ nullable: true }) marketCap!: number | null;
  @ApiProperty() timestamp!: string;
  @ApiProperty() name!: string;
  @ApiProperty() hl2!: number;
  @ApiProperty() p1!: boolean;
  @ApiProperty() p2!: boolean;
  @ApiProperty() p3!: boolean;
  @ApiProperty({ enum: ['gold', 'blue', 'gray'] })
  color!: string;

  @ApiProperty() smma_15!: number;
  @ApiProperty() smma_19!: number;
  @ApiProperty() smma_25!: number;
  @ApiProperty() smma_29!: number;
}

export class StockStatisticsDto {
  @ApiProperty({ example: 'AAPL' })
  symbol!: string;

  @ApiProperty({ example: 'USD' })
  conversionCurrency!: string;

  @ApiProperty({ type: [StockIndicatorValueDto] })
  indicatorValues!: StockIndicatorValueDto[];

  @ApiProperty({ type: StockMappingDto, nullable: true })
  mapping!: StockMappingDto | null;
}
