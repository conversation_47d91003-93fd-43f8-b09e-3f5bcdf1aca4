import { BadRequestException, Controller, Get, Logger, Query } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { StockData } from './api/financial.indicator.api';
import { API_DOCS, API_ENDPOINTS, API_ERROR_MESSAGES, API_TAGS, QUERY_PARAMS } from './constants/api';
import { StockStatisticsDto } from './dto/stock-statistics.dto';
import { StockStatisticsService } from './stock.statistics.service';

@ApiTags(API_TAGS.STOCK_STATISTICS)
@Controller()
export class StockStatisticsController {
  private readonly logger = new Logger(StockStatisticsController.name);

  constructor(
    private readonly stockStatisticsService: StockStatisticsService,
  ) {
  }

  @Get(API_ENDPOINTS.STOCK_STATISTICS)
  @ApiOperation({
    summary: API_DOCS.STOCK_STATISTICS.SUMMARY,
    description: API_DOCS.STOCK_STATISTICS.DESCRIPTION,
  })
  @ApiOkResponse({ type: [StockStatisticsDto] })
  async getStockStatistics(): Promise<StockData[]> {
    this.logger.log('Getting stock statistics');
    return this.stockStatisticsService.getStructuredStatistics();
  }

  @Get(API_ENDPOINTS.STOCK_INDICATORS)
  @ApiOperation({
    summary: API_DOCS.STOCK_INDICATORS.SUMMARY,
    description: API_DOCS.STOCK_INDICATORS.DESCRIPTION,
  })
  @ApiOkResponse({
    type: StockStatisticsDto,
    description: 'Historical stock indicator data retrieved successfully',
  })
  @ApiQuery({
    name: QUERY_PARAMS.SYMBOL.NAME,
    description: 'Stock symbol (e.g., AAPL, MSFT, GOOGL)',
    example: 'AAPL',
  })
  @ApiQuery({
    name: QUERY_PARAMS.CONVERSION_CURRENCY.NAME,
    description: QUERY_PARAMS.CONVERSION_CURRENCY.DESCRIPTION,
    example: QUERY_PARAMS.CONVERSION_CURRENCY.EXAMPLE,
  })
  async getStockIndicators(
        @Query(QUERY_PARAMS.SYMBOL.NAME) symbol: string,
        @Query(QUERY_PARAMS.CONVERSION_CURRENCY.NAME) conversionCurrency: string,
  ): Promise<StockData> {
    this.logger.log(`Getting stock indicators for ${symbol}/${conversionCurrency}`);

    if (!symbol || !conversionCurrency) {
      throw new BadRequestException(API_ERROR_MESSAGES.MISSING_REQUIRED_PARAMS);
    }

    return this.stockStatisticsService.getStockIndicators(symbol, conversionCurrency);
  }
}
