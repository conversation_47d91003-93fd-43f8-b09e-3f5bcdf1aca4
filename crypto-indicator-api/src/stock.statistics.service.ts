import { Inject, Injectable } from '@nestjs/common';

import {
  DaemonClient,
  StockData,
} from './api/financial.indicator.api';
import { StockDataTransformationService } from './services/stock-data-transformation.service';

@Injectable()
export class StockStatisticsService {
  constructor(
    @Inject('daemonClient')
    private readonly daemonClient: DaemonClient,
    private readonly transformationService: StockDataTransformationService,
  ) {}

  async getStructuredStatistics(): Promise<StockData[]> {
    const daemonResponse = await this.daemonClient.getStockStatistics();
    return this.transformationService.transform(daemonResponse);
  }

  async getStockIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<StockData> {
    const indicatorValues = await this.daemonClient.getStockIndicators(
      symbol,
      conversionCurrency,
    );

    // Return in consistent StockData format
    // Note: mapping field is not available from the getStockIndicators endpoint
    // This method is used for chart data, mapping is only available from getStructuredStatistics
    return {
      symbol,
      conversionCurrency,
      indicatorValues,
      mapping: {
        symbol,
        name: symbol, // Fallback to symbol as name
        exchange: 'Unknown', // Fallback exchange
        currency: conversionCurrency,
        country: 'Unknown',
        type: 'equity',
      },
    };
  }
}
