{"$schema": "https://unpkg.com/knip@5/schema.json", "entry": ["src/index.tsx", "src/App.tsx"], "project": ["src/**/*.{ts,tsx}", "!src/**/*.test.{ts,tsx}", "!src/**/*.spec.{ts,tsx}"], "ignore": ["src/generated/**/*", "scripts/**/*", "public/**/*", "build/**/*"], "ignoreDependencies": ["@testing-library/jest-dom", "@testing-library/react", "@testing-library/user-event", "web-vitals", "prettier", "@babel/plugin-proposal-private-property-in-object", "eslint-config-react-app"], "ignoreBinaries": ["dot"], "ignoreExportsUsedInFile": true, "includeEntryExports": false, "workspaces": {".": {"entry": ["src/index.tsx"], "project": ["src/**/*.{ts,tsx}"]}}, "rules": {"files": "error", "dependencies": "error", "devDependencies": "error", "unlisted": "error", "exports": "error", "nsExports": "error", "classMembers": "error", "types": "error", "nsTypes": "error", "enumMembers": "error", "duplicates": "error"}}