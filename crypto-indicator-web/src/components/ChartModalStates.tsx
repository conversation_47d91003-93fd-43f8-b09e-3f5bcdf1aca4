import React from "react";

import { CSS_CLASSES, UI_TEXT } from "../constants/app";

import { ChartModal } from "./chart/ChartModal";
import { ErrorState } from "./ui/ErrorState";
import { LoadingState } from "./ui/LoadingState";
import { ErrorBoundary } from "./ErrorBoundary";

import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from "../generated";

interface ChartModalStatesProps {
  showChart: boolean;
  chartData: CryptoCurrencyStatisticsDto | StockStatisticsDto | null;
  chartLoading: boolean;
  chartError: string | null;
  onClose: () => void;
}

export const ChartModalStates: React.FC<ChartModalStatesProps> = ({
  showChart,
  chartData,
  chartLoading,
  chartError,
  onClose,
}) => {
  return (
    <>
      {showChart && chartData && (
        <ErrorBoundary>
          <ChartModal data={chartData} onClose={onClose} />
        </ErrorBoundary>
      )}

      {chartLoading && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <LoadingState message={UI_TEXT.LOADING_CHART_DATA} />
          </div>
        </div>
      )}

      {(chartError !== null && chartError !== undefined) && (
        <div className={CSS_CLASSES.CHART_MODAL}>
          <div className={CSS_CLASSES.CHART_MODAL_CONTENT}>
            <ErrorState
              message={chartError}
              onRetry={onClose}
              showRetry
            />
          </div>
        </div>
      )}
    </>
  );
};
