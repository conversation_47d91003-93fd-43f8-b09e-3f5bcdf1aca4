import React, { useEffect, useMemo,useState } from "react";

import { CSS_CLASSES,TIMING } from "../constants/app";
import { useChartData } from "../hooks/useChartData";
import { useCryptoData } from "../hooks/useCryptoData";
import { useFiltering } from "../hooks/useFiltering";
import { useSorting } from "../hooks/useSorting";
import { useStockChartData } from "../hooks/useStockChartData";
import { useStockData } from "../hooks/useStockData";
import { useStockFiltering } from "../hooks/useStockFiltering";
import { useStockSorting } from "../hooks/useStockSorting";
import { useTabNavigation } from "../hooks/useTabNavigation";
import { DEFAULT_TABS } from "../types/tabs";
import { findBtcDataForSymbol, formatDate,processCryptoStatistics } from "../utils/dataProcessors";
import { processStockStatistics } from "../utils/stockDataProcessors";
import { applyStockFilters } from "../utils/stockTableFiltering";
import { applyStockSorting } from "../utils/stockTableSorting";
import { applyFilters } from "../utils/tableFiltering";
import { applySorting } from "../utils/tableSorting";

import { StockMainContent } from "./stock/StockMainContent";
import { DashboardHeader } from "./ui/DashboardHeader";
import { TabNavigation } from "./ui/TabNavigation";
import { AppStateHandler } from "./AppStateHandler";
import { ChartModalStates } from "./ChartModalStates";
import { MainContent } from "./MainContent";

// Component-specific styles
import '../styles/components/table.css';

// eslint-disable-next-line max-lines-per-function
const StatisticsTable: React.FC = () => {
    const [showChart, setShowChart] = useState(false);

    const { data: statistics, loading, error, fetchData } = useCryptoData();
    const { chartData, chartLoading, chartError, fetchChartData, clearChartData } = useChartData();
    const { sortConfig, handleSort, getSortDirection } = useSorting();
    const {
        filterConfig,
        updateSymbolSearch,
        updateUsdSignal,
        updateBtcSignal,
        clearFilters,
        hasActiveFilters,
    } = useFiltering();

    // Process data with memoization for performance
    const { usdStatistics, btcStatistics, totalCount } = useMemo(() =>
        processCryptoStatistics(statistics), [statistics]
    );

    // Apply filtering and sorting with memoization for performance
    const processedData = useMemo(() => {
        const filtered = applyFilters(usdStatistics, btcStatistics, filterConfig, findBtcDataForSymbol);
        return applySorting(filtered, btcStatistics, sortConfig, findBtcDataForSymbol);
    }, [usdStatistics, btcStatistics, filterConfig, sortConfig]);

    const filteredCount = processedData.length;

    const handleSignalClick = async (symbol: string, conversionCurrency: string) => {
        await fetchChartData(symbol, conversionCurrency);
        setShowChart(true);
    };

    const closeChart = () => {
        setShowChart(false);
        clearChartData();
    };

    useEffect(() => {
        const handleFetchData = () => {
            fetchData().catch((error) => {
                // eslint-disable-next-line no-console
                console.error('Failed to fetch data:', error);
            });
        };

        handleFetchData();
        const interval = setInterval(handleFetchData, TIMING.DATA_REFRESH_INTERVAL);
        return () => { clearInterval(interval); };
    }, [fetchData]);

    return (
        <AppStateHandler
            loading={loading}
            error={error}
            statisticsLength={statistics.length}
            onRetry={() => {
                fetchData().catch((error) => {
                    // eslint-disable-next-line no-console
                    console.error('Failed to retry fetch data:', error);
                });
            }}
        >
            <div className={CSS_CLASSES.APP_CONTAINER}>
                <DashboardHeader />

                <MainContent
                    processedData={processedData}
                    btcStatistics={btcStatistics}
                    totalCount={totalCount}
                    filteredCount={filteredCount}
                    loading={loading}
                    filterConfig={filterConfig}
                    hasActiveFilters={hasActiveFilters}
                    onSignalClick={handleSignalClick}
                    onRefresh={() => {
                        fetchData().catch((error) => {
                            // eslint-disable-next-line no-console
                            console.error('Failed to refresh data:', error);
                        });
                    }}
                    onSort={handleSort}
                    getSortDirection={getSortDirection}
                    onSymbolSearchChange={updateSymbolSearch}
                    onUsdSignalChange={updateUsdSignal}
                    onBtcSignalChange={updateBtcSignal}
                    onClearFilters={clearFilters}
                    formatDate={formatDate}
                    findBtcDataForSymbol={findBtcDataForSymbol}
                />

                <ChartModalStates
                    showChart={showChart}
                    chartData={chartData}
                    chartLoading={chartLoading}
                    chartError={chartError}
                    onClose={closeChart}
                />
            </div>
        </AppStateHandler>
    );
};

export default StatisticsTable;
