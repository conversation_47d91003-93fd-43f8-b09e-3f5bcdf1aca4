import React from 'react';

import type { StockFilterConfig } from '../../types/table';

interface StockFilterControlsProps {
  filterConfig: StockFilterConfig;
  hasActiveFilters: boolean;
  loading: boolean;
  onSymbolSearchChange: (search: string) => void;
  onSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onClearFilters: () => void;
  onRefresh: () => void;
}

export const StockFilterControls: React.FC<StockFilterControlsProps> = ({
  filterConfig,
  hasActiveFilters,
  loading,
  onSymbolSearchChange,
  onSignalChange,
  onClearFilters,
  onRefresh,
}) => {
  return (
    <div className="filter-controls">
      <input
        type="text"
        placeholder="Search stocks..."
        value={filterConfig.symbolSearch}
        onChange={(e) => { onSymbolSearchChange(e.target.value); }}
        className="search-input"
      />
      
      <select
        value={filterConfig.signal}
        onChange={(e) => { onSignalChange(e.target.value as 'all' | 'gold' | 'blue' | 'gray'); }}
        className="signal-filter"
      >
        <option value="all">All Signals</option>
        <option value="gold">Bullish (Gold)</option>
        <option value="blue">Bearish (Blue)</option>
        <option value="gray">Neutral (Gray)</option>
      </select>

      {hasActiveFilters && (
        <button type="button" onClick={onClearFilters} className="clear-filters-btn">
          Clear Filters
        </button>
      )}

      <button type="button" onClick={onRefresh} disabled={loading} className="refresh-btn">
        {loading ? 'Refreshing...' : 'Refresh'}
      </button>
    </div>
  );
};
