import React from 'react';

import { StockFilterControls } from './StockFilterControls';
import { StockTable } from './StockTable';

import type { StockStatisticsDto } from '../../generated';
import type { StockFilterConfig, StockSortColumn } from '../../types/table';

interface StockMainContentProps {
  data: StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  filterConfig: StockFilterConfig;
  hasActiveFilters: boolean;
  onSignalClick: (symbol: string, conversionCurrency: string) => void;
  onRefresh: () => void;
  onSort: (column: StockSortColumn) => void;
  getSortDirection: (column: StockSortColumn) => 'asc' | 'desc' | null;
  onSymbolSearchChange: (search: string) => void;
  onSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onClearFilters: () => void;
}

export const StockMainContent: React.FC<StockMainContentProps> = ({
  data,
  totalCount,
  filteredCount,
  loading,
  filterConfig,
  hasActiveFilters,
  onSignalClick,
  onRefresh,
  onSort,
  getSortDirection,
  onSymbolSearchChange,
  onSignalChange,
  onClearFilters,
}) => {
  return (
    <div className="stock-main-content">
      <div className="content-header">
        <h2>Stock Statistics</h2>
        <p>
          Showing {filteredCount} of {totalCount} stocks
          {hasActiveFilters && ' (filtered)'}
        </p>
      </div>

      {/* Search and Filter Controls */}
      <StockFilterControls
        filterConfig={filterConfig}
        hasActiveFilters={hasActiveFilters}
        loading={loading}
        onSymbolSearchChange={onSymbolSearchChange}
        onSignalChange={onSignalChange}
        onClearFilters={onClearFilters}
        onRefresh={onRefresh}
      />

      {/* Stock Table */}
      <StockTable
        data={data}
        onSort={onSort}
        getSortDirection={getSortDirection}
        onSignalClick={onSignalClick}
      />

      {data.length === 0 && !loading && (
        <div className="no-data">
          <p>No stock data available.</p>
        </div>
      )}
    </div>
  );
};
