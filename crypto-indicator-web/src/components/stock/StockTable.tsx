import React from 'react';

import type { StockStatisticsDto } from '../../generated';
import type { StockSortColumn } from '../../types/table';

/**
 * Get sort direction indicator
 */
const getSortIndicator = (direction: 'asc' | 'desc' | null): string => {
  if (direction === 'asc') {
    return '↑';
  }
  if (direction === 'desc') {
    return '↓';
  }
  return '';
};

/**
 * Format price value
 */
const formatPrice = (price: number | undefined): string => {
  return price?.toFixed(2) ?? 'N/A';
};

/**
 * Format volume value
 */
const formatVolume = (volume: number | undefined): string => {
  return volume?.toLocaleString() ?? 'N/A';
};

interface StockTableProps {
  data: StockStatisticsDto[];
  onSort: (column: StockSortColumn) => void;
  getSortDirection: (column: StockSortColumn) => 'asc' | 'desc' | null;
  onSignalClick: (symbol: string, conversionCurrency: string) => void;
}

export const StockTable: React.FC<StockTableProps> = ({
  data,
  onSort,
  getSortDirection,
  onSignalClick,
}) => {
  return (
    <div className="stock-table-container">
      <table className="stock-table">
        <thead>
          <tr>
            <th>
              <button type="button" onClick={() => { onSort('symbol'); }}>
                Symbol {getSortIndicator(getSortDirection('symbol'))}
              </button>
            </th>
            <th>
              <button type="button" onClick={() => { onSort('price'); }}>
                Price {getSortIndicator(getSortDirection('price'))}
              </button>
            </th>
            <th>
              <button type="button" onClick={() => { onSort('volume'); }}>
                Volume {getSortIndicator(getSortDirection('volume'))}
              </button>
            </th>
            <th>
              <button type="button" onClick={() => { onSort('signal'); }}>
                Signal {getSortIndicator(getSortDirection('signal'))}
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((stock) => {
            const latestData = stock.indicatorValues.at(-1);
            return (
              <tr key={stock.symbol}>
                <td>{stock.symbol}</td>
                <td>{formatPrice(latestData?.close)}</td>
                <td>{formatVolume(latestData?.volume)}</td>
                <td>
                  <button
                    type="button"
                    onClick={() => { onSignalClick(stock.symbol, stock.conversionCurrency); }}
                    className={`signal-badge signal-${latestData?.color ?? 'gray'}`}
                  >
                    {latestData?.color ?? 'N/A'}
                  </button>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
