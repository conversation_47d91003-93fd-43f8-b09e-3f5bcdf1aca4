import React from "react";

// Component-specific styles
import '../../styles/utilities/loading-error.css';

interface LoadingStateProps {
  message?: string;
  size?: "small" | "medium" | "large";
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = "Loading...",
  size = "medium",
}) => {
  const sizeClass = `loading-${size}`;

  return (
    <div className={`loading-container ${sizeClass}`}>
      <div className="loading-spinner" />
      <p className="loading-message">{message}</p>
    </div>
  );
};
