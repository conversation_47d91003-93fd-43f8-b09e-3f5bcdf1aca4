import { useCallback, useState } from "react";

import { defaultApiClient } from "../generated";
import { StockDataService } from "../services/StockDataService";

import type { StockStatisticsDto } from "../generated";

interface UseStockChartDataReturn {
  chartData: StockStatisticsDto | null;
  chartLoading: boolean;
  chartError: string | null;
  fetchChartData: (symbol: string, conversionCurrency: string) => Promise<void>;
  clearChartData: () => void;
}

const stockDataService = new StockDataService(defaultApiClient);

export const useStockChartData = (): UseStockChartDataReturn => {
  const [chartData, setChartData] = useState<StockStatisticsDto | null>(null);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);

  const fetchChartData = useCallback(async (
    symbol: string,
    conversionCurrency: string,
  ): Promise<void> => {
    setChartLoading(true);
    setChartError(null);

    try {
      const data = await stockDataService.getIndicators(symbol, conversionCurrency);
      setChartData(data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setChartError(errorMessage);
      // eslint-disable-next-line no-console
      console.error("Failed to fetch stock chart data:", error);
    } finally {
      setChartLoading(false);
    }
  }, []);

  const clearChartData = useCallback((): void => {
    setChartData(null);
    setChartError(null);
  }, []);

  return {
    chartData,
    chartLoading,
    chartError,
    fetchChartData,
    clearChartData,
  };
};
