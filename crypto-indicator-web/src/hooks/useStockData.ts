import { useCallback, useState } from "react";

import { defaultApiClient } from "../generated";
import { StockDataService } from "../services/StockDataService";

import type { StockStatisticsDto } from "../generated";

interface UseStockDataReturn {
  data: StockStatisticsDto[];
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
}

const stockDataService = new StockDataService(defaultApiClient);

export const useStockData = (): UseStockDataReturn => {
  const [data, setData] = useState<StockStatisticsDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async (): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const statistics = await stockDataService.getStatistics();
      setData(statistics);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setError(errorMessage);
      // eslint-disable-next-line no-console
      console.error("Failed to fetch stock data:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    data,
    loading,
    error,
    fetchData,
  };
};
