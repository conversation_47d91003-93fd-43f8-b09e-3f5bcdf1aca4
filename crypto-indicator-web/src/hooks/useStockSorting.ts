import { useCallback, useState } from "react";

import type { SortDirection, StockSortColumn, StockSortConfig } from "../types/table";

interface UseStockSortingReturn {
  sortConfig: StockSortConfig;
  handleSort: (column: StockSortColumn) => void;
  getSortDirection: (column: StockSortColumn) => SortDirection;
}

const DEFAULT_SORT_CONFIG: StockSortConfig = {
  column: null,
  direction: null,
};

export const useStockSorting = (): UseStockSortingReturn => {
  const [sortConfig, setSortConfig] = useState<StockSortConfig>(DEFAULT_SORT_CONFIG);

  const handleSort = useCallback((column: StockSortColumn): void => {
    setSortConfig(prev => {
      if (prev.column === column) {
        // Cycle through: asc -> desc -> null
        if (prev.direction === 'asc') {
          return { column, direction: 'desc' };
        }
        if (prev.direction === 'desc') {
          return { column: null, direction: null };
        }
      }
      
      // Start with ascending
      return { column, direction: 'asc' };
    });
  }, []);

  const getSortDirection = useCallback((column: StockSortColumn): SortDirection => {
    return sortConfig.column === column ? sortConfig.direction : null;
  }, [sortConfig]);

  return {
    sortConfig,
    handleSort,
    getSortDirection,
  };
};
