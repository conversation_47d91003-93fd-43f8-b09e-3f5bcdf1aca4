import type {
  CryptoIndicatorApiClient,
  StockStatisticsDto,
} from "../generated";

/**
 * Service layer for stock data operations
 * Implements business logic and abstracts API calls
 */
export class StockDataService {
  constructor(private readonly apiClient: CryptoIndicatorApiClient) {}

  /**
   * Fetch all stock statistics
   */
  async getStatistics(): Promise<StockStatisticsDto[]> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return await this.apiClient.StockStatisticsController_getStockStatistics();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("Failed to fetch stock statistics:", error);
      throw new Error("Unable to fetch stock data. Please try again.");
    }
  }

  /**
   * Fetch historical indicator data for a specific stock
   */
  async getIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<StockStatisticsDto> {
    if (!symbol || !conversionCurrency) {
      throw new Error("Symbol and conversion currency are required");
    }

    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return await this.apiClient.StockStatisticsController_getStockIndicators(
        symbol,
        conversionCurrency,
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        `Failed to fetch indicators for ${symbol}/${conversionCurrency}:`,
        error,
      );
      throw new Error(
        `Unable to fetch chart data for ${symbol}/${conversionCurrency}. Please try again.`,
      );
    }
  }
}
