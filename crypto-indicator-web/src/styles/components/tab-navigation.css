/**
 * Tab Navigation Component Styles
 * 
 * Styles for the TabNavigation component including tab buttons,
 * active states, and responsive design.
 * 
 * Dependencies: variables.css, base.css
 */

.tab-navigation {
  margin-bottom: 24px;
}

.tab-list {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  box-shadow: var(--glass-shadow);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-width: 100px;
  justify-content: center;
}

.tab-button:hover {
  background: var(--bg-elevated);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-teal);
}

.tab-button.active {
  background: var(--accent-teal);
  color: var(--bg-primary);
  box-shadow: 0 2px 8px rgba(148, 226, 213, 0.3);
}

.tab-button.active:hover {
  background: var(--accent-teal);
  color: var(--bg-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 226, 213, 0.4);
}

.tab-icon {
  font-size: 16px;
  line-height: 1;
}

.tab-label {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .tab-navigation {
    margin-bottom: 16px;
  }
  
  .tab-button {
    padding: 10px 16px;
    font-size: 13px;
    min-width: 80px;
  }
  
  .tab-icon {
    font-size: 14px;
  }
}

/* Tablet responsive */
@media (max-width: 768px) {
  .tab-button {
    padding: 11px 18px;
    min-width: 90px;
  }
}
