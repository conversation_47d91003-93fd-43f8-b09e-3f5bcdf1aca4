export type AssetType = 'crypto' | 'stock';

export interface TabConfig {
  id: AssetType;
  label: string;
  icon?: string;
}

export interface TabNavigationProps {
  activeTab: AssetType;
  onTabChange: (tab: AssetType) => void;
  tabs: TabConfig[];
}

export const DEFAULT_TABS: TabConfig[] = [
  {
    id: 'crypto',
    label: 'Crypto',
    icon: '₿',
  },
  {
    id: 'stock',
    label: 'Stocks',
    icon: '📈',
  },
] as const;
