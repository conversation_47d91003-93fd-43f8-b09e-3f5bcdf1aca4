import { DATA_PROCESSING } from "../constants/app";

import type { StockIndicatorValueDto, StockStatisticsDto } from "../generated";

/**
 * Process stock statistics data for table display
 */
export const processStockStatistics = (statistics: StockStatisticsDto[]) => {
  const stockStatistics = statistics.filter(
    (stat): stat is StockStatisticsDto => 
      stat.indicatorValues.length > 0
  );

  return {
    stockStatistics,
    totalCount: stockStatistics.length,
  };
};

/**
 * Get the latest indicator value for a stock
 */
export const getLatestStockData = (stock: StockStatisticsDto): StockIndicatorValueDto | undefined => {
  if (stock.indicatorValues.length === 0) {
    return undefined;
  }
  
  // Return the most recent indicator value (last in array)
  return stock.indicatorValues[stock.indicatorValues.length - 1];
};

/**
 * Format stock symbol for display (remove exchange suffix if needed)
 */
export const formatStockSymbol = (symbol: string): string => {
  // For stocks like "EGR1T.TL", we might want to show just "EGR1T" or keep the full symbol
  // For now, keep the full symbol to maintain clarity about which exchange
  return symbol;
};

/**
 * Get stock icon/emoji for display
 */
export const getStockIcon = (symbol: string): string => {
  // Simple logic to get first 2 characters as "icon"
  return symbol.slice(0, DATA_PROCESSING.CRYPTO_ICON_LENGTH);
};

/**
 * Format stock price with currency
 */
export const formatStockPrice = (
  price: number | undefined, 
  currency: string = 'USD'
): string => {
  if (price === undefined || price === null) {
    return DATA_PROCESSING.DEFAULT_VALUE;
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  }).format(price);
};

/**
 * Format stock volume
 */
export const formatStockVolume = (volume: number | undefined): string => {
  if (volume === undefined || volume === null) {
    return DATA_PROCESSING.DEFAULT_VALUE;
  }

  if (volume >= 1_000_000) {
    return `${(volume / 1_000_000).toFixed(1)}M`;
  }
  
  if (volume >= 1_000) {
    return `${(volume / 1_000).toFixed(1)}K`;
  }
  
  return volume.toLocaleString();
};
