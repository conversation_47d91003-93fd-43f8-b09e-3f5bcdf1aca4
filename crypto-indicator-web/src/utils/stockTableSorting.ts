import { getLatestStockData } from "./stockDataProcessors";

import type { StockSortConfig } from "../types/table";
import type { StockStatisticsDto } from "../generated";

/**
 * Apply sorting to stock data
 */
export const applyStockSorting = (
  data: StockStatisticsDto[],
  sortConfig: StockSortConfig,
): StockStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const aData = getLatestStockData(a);
    const bData = getLatestStockData(b);

    let aValue: string | number = '';
    let bValue: string | number = '';

    switch (sortConfig.column) {
      case 'symbol': {
        aValue = a.symbol;
        bValue = b.symbol;
        break;
      }
      case 'price': {
        aValue = aData?.close ?? 0;
        bValue = bData?.close ?? 0;
        break;
      }
      case 'volume': {
        aValue = aData?.volume ?? 0;
        bValue = bData?.volume ?? 0;
        break;
      }
      case 'signal': {
        // Sort by signal color: gold > blue > gray
        const signalOrder = { gold: 3, blue: 2, gray: 1 };
        aValue = signalOrder[aData?.color as keyof typeof signalOrder] ?? 0;
        bValue = signalOrder[bData?.color as keyof typeof signalOrder] ?? 0;
        break;
      }
      default: {
        return 0;
      }
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      const comparison = aValue - bValue;
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    }

    return 0;
  });
};
